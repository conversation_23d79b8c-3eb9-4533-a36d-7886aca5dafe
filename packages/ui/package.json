{"name": "@satur/ui", "version": "0.0.1", "private": true, "main": "index.ts", "types": "index.ts", "scripts": {"build": "echo 'Build skipado temporariamente'", "dev": "echo 'Dev mode ativo'", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "eslint": "^9.27.0", "typescript": "~5.8.3"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "dependencies": {"@headlessui/react": "^2.2.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.511.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0"}}