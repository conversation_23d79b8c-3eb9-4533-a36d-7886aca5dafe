{"permissions": {"allow": ["Bash(pnpm prisma migrate dev:*)", "Bash(ls:*)", "Bash(pnpm prisma migrate:*)", "Bash(pnpm prisma generate:*)", "Bash(pnpm type-check:*)", "Bash(pnpm build:*)", "Bash(pnpm add:*)", "Bash(pnpm lint:*)", "Bash(npm ls:*)", "Bash(pnpm ls:*)", "Bash(pnpm dev:*)", "Bash(pnpm web:dev:*)", "Bash(pnpm db:reset:*)", "Bash(pnpm prisma:seed:*)", "Bash(pnpm prisma:generate:*)", "Bash(pnpm prisma:*)"], "deny": []}}