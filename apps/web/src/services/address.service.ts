import axios from 'axios';

const viaCepApi = axios.create({
  baseURL: 'https://viacep.com.br/ws',
});

interface ViaCepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
  erro?: boolean;
}

interface AddressData {
  zipcode: string;
  street: string;
  complement: string;
  neighborhood: string;
  city: string;
  state: string;
  country: string;
}

const validateCep = async (cep: string): Promise<{ success: boolean; data?: AddressData }> => {
  try {
    const response = await viaCepApi.get<ViaCepResponse>(`${cep}/json`);
    const { data } = response;

    if (data.erro) {
      return { success: false };
    }

    return {
      success: true,
      data: {
        zipcode: data.cep,
        street: data.logradouro,
        complement: data.complemento,
        neighborhood: data.bairro,
        city: data.localidade,
        state: data.uf,
        country: 'Brasil',
      },
    };
  } catch (error) {
    console.error('Error validating CEP:', error);
    throw new Error('Error validating CEP');
  }
};

const cleanCep = (cep: string): string => {
  return cep.replace(/\D/g, '');
};

const formatCep = (cep: string): string => {
  return cep
    .replace(/\D/g, '')
    .replace(/(\d{5})(\d)/, '$1-$2')
    .substring(0, 9);
};

export const addressService = {
  validateCep,
  cleanCep,
  formatCep,
};
