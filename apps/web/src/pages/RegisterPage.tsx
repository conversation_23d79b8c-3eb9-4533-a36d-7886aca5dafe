import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link, useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle, Button, Input, Label } from '@satur/ui';
import { usersApi } from '../services/api';
import { useAuthStore } from '../stores/auth.store';
import { authService } from '../services/auth.service';
import { ApiError } from '../lib/api';

import {
  isCPF,
  isCNPJ,
  formatToCPF,
  formatToCNPJ,
  formatToPhone
} from 'brazilian-values';

// Schema de validação
const registerSchema = z.object({
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres'),
  email: z.string().email('Email deve ter um formato válido'),
  password: z.string().min(6, '<PERSON>ha deve ter pelo menos 6 caracteres'),
  document: z.string().min(11, 'Documento é obrigatório'),
  phone: z.string().min(10, 'Telefone é obrigatório'),
}).refine((data) => {
  const doc = data.document.replace(/\D/g, '');
  if (doc.length === 11) {
    return isCPF(doc);
  }
  if (doc.length === 14) {
    return isCNPJ(doc);
  }
  return false;
}, {
  message: 'CPF ou CNPJ inválido',
  path: ['document'],
});

type RegisterFormData = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const [error, setError] = useState<string>('');

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const documentValue = watch('document');
  const phoneValue = watch('phone');

  // Mutation para registro
  const { mutate: registerUser, isPending: isRegistering } = useMutation({
    mutationFn: usersApi.create,
    onSuccess: async (user) => {
      // Fazer login automático após registro
      try {
        const loginResponse = await authService.login({
          email: user.email,
          password: watch('password'),
        });

        login(loginResponse.access_token, loginResponse.user);
        navigate('/reports');
      } catch {
        navigate('/login');
      }
    },
    onError: (error: ApiError) => {
      setError(error.message || 'Erro ao criar conta');
    },
  });

  const onSubmit = (data: RegisterFormData) => {
    setError('');
    const doc = data.document.replace(/\D/g, '');
    const documentType: 'CPF' | 'CNPJ' = doc.length === 11 ? 'CPF' : 'CNPJ';

    const cleanData = {
      ...data,
      document: doc,
      phone: data.phone.replace(/\D/g, ''),
      role: 'CUSTOMER' as const,
      documentType,
    };

    registerUser(cleanData);
  };

  // Formatação automática de campos
  const handleDocumentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    let formatted = value;

    if (value.length <= 11) {
      formatted = formatToCPF(value);
    } else {
      formatted = formatToCNPJ(value);
    }

    setValue('document', formatted);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '');
    const formatted = formatToPhone(value);
    setValue('phone', formatted);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-xl border-0">
        <CardHeader className="text-center pb-6">
          <CardTitle className="text-2xl font-bold text-gray-800">
            Criar Conta
          </CardTitle>
          <p className="text-gray-600 mt-2">
            Preencha os dados para criar sua conta
          </p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            {/* Nome */}
            <div>
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                {...register('name')}
                className="mt-1"
                placeholder="Seu nome completo"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                className="mt-1"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            {/* Senha */}
            <div>
              <Label htmlFor="password">Senha</Label>
              <Input
                id="password"
                type="password"
                {...register('password')}
                className="mt-1"
                placeholder="Mínimo 6 caracteres"
              />
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
              )}
            </div>

            {/* Documento */}
            <div>
              <Label htmlFor="document">
                CPF ou CNPJ
              </Label>
              <Input
                id="document"
                value={documentValue || ''}
                onChange={handleDocumentChange}
                className="mt-1"
                placeholder="Digite seu CPF ou CNPJ"
              />
              {errors.document && (
                <p className="text-red-500 text-sm mt-1">{errors.document.message}</p>
              )}
            </div>

            {/* Telefone */}
            <div>
              <Label htmlFor="phone">Telefone</Label>
              <Input
                id="phone"
                value={phoneValue || ''}
                onChange={handlePhoneChange}
                className="mt-1"
                placeholder="(11) 99999-9999"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
              )}
            </div>

            {/* Botões */}
            <div className="space-y-3 pt-4">
              <Button
                type="submit"
                disabled={isRegistering}
                className="w-full h-12 bg-blue-700 hover:bg-blue-800 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRegistering ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Criando conta...
                  </div>
                ) : (
                  'Criar Conta'
                )}
              </Button>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Já tem uma conta?{' '}
                  <Link
                    to="/login"
                    className="text-blue-700 hover:text-blue-800 font-medium hover:underline transition-colors"
                  >
                    Fazer Login
                  </Link>
                </p>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}