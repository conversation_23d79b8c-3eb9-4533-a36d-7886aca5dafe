export interface User {
  id: number;
  name: string;
  email: string;
  role: 'AGENCY' | 'SUPPLIER' | 'ADMIN' | 'CUSTOMER';
  phone?: string;
  photoUrl?: string;
  isApproved: boolean;
  document?: string;
  documentType?: 'CPF' | 'CNPJ';
  // Address fields (optional)
  zipcode?: string;
  street?: string;
  neighborhood?: string;
  complement?: string;
  city?: string;
  state?: string;
  country?: string;
  streetNumber?: string;
  locationId?: number;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  user: User;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}
