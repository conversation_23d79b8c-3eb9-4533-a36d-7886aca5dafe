import { useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { Input, Label } from '@satur/ui';
import { addressService } from '../../services/address.service';
import { Loader2, MapPin, AlertCircle } from 'lucide-react';

interface AddressFieldsProps {
  form: UseFormReturn<any>;
  disabled?: boolean;
  onAddressFound?: (data: any) => void;
  onAddressNotFound?: () => void;
  onError?: (error: string) => void;
}

export function AddressFields({
  form,
  disabled = false,
  onAddressFound,
  onAddressNotFound,
  onError,
}: AddressFieldsProps) {
  const [isValidatingCep, setIsValidatingCep] = useState(false);
  const [cepError, setCepError] = useState<string>('');

  const { register, watch, setValue, formState: { errors } } = form;
  const zipcodeValue = watch('zipcode');

  const handleCepChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const cleanValue = addressService.cleanCep(rawValue);
    const formattedValue = addressService.formatCep(cleanValue);
    
    setValue('zipcode', formattedValue);
    setCepError('');

    // Only validate if we have a complete CEP
    if (cleanValue.length === 8) {
      setIsValidatingCep(true);
      
      try {
        const result = await addressService.validateCep(cleanValue);
        
        if (result.success && result.data) {
          // Auto-fill address fields
          setValue('street', result.data.street);
          setValue('neighborhood', result.data.neighborhood);
          setValue('city', result.data.city);
          setValue('state', result.data.state);
          setValue('country', 'Brasil');
          
          if (result.data.complement) {
            setValue('complement', result.data.complement);
          }
          
          onAddressFound?.(result.data);
        } else {
          setCepError('CEP não encontrado. Verifique o CEP digitado.');
          onAddressNotFound?.();
        }
      } catch {
        setCepError('Erro ao validar CEP. Tente novamente.');
        onError?.('Erro ao validar CEP');
      } finally {
        setIsValidatingCep(false);
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* CEP */}
      <div>
        <Label htmlFor="zipcode" className="flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          CEP
        </Label>
        <div className="relative">
          <Input
            id="zipcode"
            value={zipcodeValue || ''}
            onChange={handleCepChange}
            disabled={disabled}
            className="mt-1"
            placeholder="00000-000"
            maxLength={10}
          />
          {isValidatingCep && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            </div>
          )}
        </div>
        {errors.zipcode && (
          <p className="text-red-500 text-sm mt-1">{String(errors.zipcode?.message)}</p>
        )}
        {cepError && (
          <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {cepError}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Rua */}
        <div>
          <Label htmlFor="street">Rua</Label>
          <Input
            id="street"
            {...register('street')}
            disabled={disabled}
            className="mt-1"
            placeholder="Nome da rua"
          />
          {errors.street && (
            <p className="text-red-500 text-sm mt-1">{String(errors.street?.message)}</p>
          )}
        </div>

        {/* Número */}
        <div>
          <Label htmlFor="streetNumber">Número</Label>
          <Input
            id="streetNumber"
            {...register('streetNumber')}
            disabled={disabled}
            className="mt-1"
            placeholder="Número da casa/prédio"
          />
          {errors.streetNumber && (
            <p className="text-red-500 text-sm mt-1">{String(errors.streetNumber?.message)}</p>
          )}
        </div>

        {/* Bairro */}
        <div>
          <Label htmlFor="neighborhood">Bairro</Label>
          <Input
            id="neighborhood"
            {...register('neighborhood')}
            disabled={disabled}
            className="mt-1"
            placeholder="Nome do bairro"
          />
          {errors.neighborhood && (
            <p className="text-red-500 text-sm mt-1">{String(errors.neighborhood?.message)}</p>
          )}
        </div>

        {/* Complemento */}
        <div>
          <Label htmlFor="complement">Complemento</Label>
          <Input
            id="complement"
            {...register('complement')}
            disabled={disabled}
            className="mt-1"
            placeholder="Apto, bloco, casa, etc."
          />
          {errors.complement && (
            <p className="text-red-500 text-sm mt-1">{String(errors.complement?.message)}</p>
          )}
        </div>

        {/* Cidade */}
        <div>
          <Label htmlFor="city">Cidade</Label>
          <Input
            id="city"
            {...register('city')}
            disabled={disabled}
            className="mt-1"
            placeholder="Nome da cidade"
          />
          {errors.city && (
            <p className="text-red-500 text-sm mt-1">{String(errors.city?.message)}</p>
          )}
        </div>

        {/* Estado */}
        <div>
          <Label htmlFor="state">Estado</Label>
          <Input
            id="state"
            {...register('state')}
            disabled={disabled}
            className="mt-1"
            placeholder="UF"
            maxLength={2}
          />
          {errors.state && (
            <p className="text-red-500 text-sm mt-1">{String(errors.state?.message)}</p>
          )}
        </div>
      </div>

      {/* País */}
      <div>
        <Label htmlFor="country">País</Label>
        <Input
          id="country"
          {...register('country')}
          disabled={disabled}
          className="mt-1"
          placeholder="País"
          defaultValue="Brasil"
        />
        {errors.country && (
          <p className="text-red-500 text-sm mt-1">{String(errors.country?.message)}</p>
        )}
      </div>
    </div>
  );
}