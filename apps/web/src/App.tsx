import { Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { RoleGuard } from './components/RoleGuard';
import { ProtectedRoute } from './components/ProtectedRoute';
import { RoleBasedRedirect } from './components/RoleBasedRedirect';
import { MainLayout } from './components/Layout/MainLayout';

import { UsersPage } from './pages/UsersPage';
import UserDetailsPage from './pages/UserDetailsPage';
import { ServicesPage } from './pages/ServicesPage';
import ServiceDetailsPage from './pages/ServiceDetailsPage';
import { ProductsPage } from './pages/ProductsPage';
import { ProductDetailsPage } from './pages/ProductDetailsPage';
import { ProductEditPage } from './pages/ProductEditPage';
import { PaymentsPage } from './pages/PaymentsPage';
import PaymentDetailsPage from './pages/PaymentDetailsPage';
import { ReportsPage } from './pages/ReportsPage';
import { SettingsPage } from './pages/SettingsPage';
import LocationsPage from './pages/LocationsPage';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ProtectedRoute>
        <MainLayout>
          <RoleBasedRedirect />
          <Routes>
            <Route path="/" element={<Navigate to="/reports" replace />} />
            <Route path="/dashboard" element={<Navigate to="/reports" replace />} />
            <Route path="/users" element={<RoleGuard roles={['ADMIN']}><UsersPage /></RoleGuard>} />
            <Route path="/users/:id" element={<RoleGuard roles={['ADMIN']}><UserDetailsPage /></RoleGuard>} />
            <Route path="/services" element={<RoleGuard roles={['ADMIN']}><ServicesPage /></RoleGuard>} />
            <Route path="/services/:id" element={<RoleGuard roles={['ADMIN']}><ServiceDetailsPage /></RoleGuard>} />
            <Route path="/products" element={<RoleGuard roles={['ADMIN']}><ProductsPage /></RoleGuard>} />
            <Route path="/products/:id" element={<RoleGuard roles={['ADMIN']}><ProductDetailsPage /></RoleGuard>} />
            <Route path="/products/:id/edit" element={<RoleGuard roles={['ADMIN']}><ProductEditPage /></RoleGuard>} />
            <Route path="/payments" element={<RoleGuard roles={['ADMIN']}><PaymentsPage /></RoleGuard>} />
            <Route path="/payments/:id" element={<RoleGuard roles={['ADMIN']}><PaymentDetailsPage /></RoleGuard>} />
            <Route path="/reports" element={<ReportsPage />} />
            <Route path="/locations" element={<RoleGuard roles={['ADMIN']}><LocationsPage /></RoleGuard>} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </MainLayout>
      </ProtectedRoute>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App
