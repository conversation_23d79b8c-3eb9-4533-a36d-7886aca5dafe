import { execSync } from 'child_process';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

describe('create-admin script', () => {
  afterAll(async () => {
    await prisma.$disconnect();
  });

  it('should create a new admin user', async () => {
    const name = 'Test Admin';
    const email = '<EMAIL>';
    const password = 'password123';

    // Run the create-admin script
    execSync(`ts-node ./prisma/create-admin.ts "${name}" "${email}" "${password}"`, {
      env: process.env,
    });

    // Check if the user was created in the database
    const admin = await prisma.user.findUnique({
      where: { email },
    });

    expect(admin).not.toBeNull();
    expect(admin?.name).toBe(name);
    expect(admin?.role).toBe('ADMIN');

    // Clean up the created user
    if (admin) {
      await prisma.user.delete({ where: { id: admin.id } });
    }
  });
});
