import { UserRole, DocumentType } from '@prisma/client';
import { Exclude } from 'class-transformer';

export class UserEntity {
  id: number;
  name: string;
  email: string;
  phone?: string | null;
  photoUrl?: string | null;
  role: UserRole;
  isApproved: boolean;
  document?: string | null;
  documentType?: DocumentType | null;
  // Address fields (optional)
  zipcode?: string | null;
  street?: string | null;
  neighborhood?: string | null;
  complement?: string | null;
  city?: string | null;
  state?: string | null;
  country?: string | null;
  streetNumber?: string | null;
  locationId?: number | null;
  @Exclude()
  password: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(partial: Partial<UserEntity>) {
    Object.assign(this, partial);
  }
}
