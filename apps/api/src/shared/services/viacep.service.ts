import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

export interface ViaCepResponse {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
  erro?: boolean;
}

export interface AddressData {
  zipcode: string;
  street: string;
  neighborhood: string;
  city: string;
  state: string;
  complement?: string;
}

@Injectable()
export class ViaCepService {
  private readonly logger = new Logger(ViaCepService.name);
  private readonly baseUrl = 'https://viacep.com.br/ws';

  constructor(private readonly httpService: HttpService) {}

  async getAddressByZipcode(zipcode: string): Promise<AddressData | null> {
    try {
      // Remove non-numeric characters from zipcode
      const cleanZipcode = zipcode.replace(/\D/g, '');

      // Validate zipcode format (8 digits)
      if (cleanZipcode.length !== 8) {
        this.logger.warn(`Invalid zipcode format: ${zipcode}`);
        return null;
      }

      const url = `${this.baseUrl}/${cleanZipcode}/json/`;
      this.logger.debug(`Fetching address for zipcode: ${cleanZipcode}`);

      const response = await firstValueFrom(
        this.httpService.get<ViaCepResponse>(url),
      );

      const data = response.data;

      // Check if ViaCEP returned an error
      if (data.erro) {
        this.logger.warn(`Address not found for zipcode: ${cleanZipcode}`);
        return null;
      }

      // Transform ViaCEP response to our format
      const addressData: AddressData = {
        zipcode: data.cep,
        street: data.logradouro,
        neighborhood: data.bairro,
        city: data.localidade,
        state: data.uf,
        complement: data.complemento || undefined,
      };

      this.logger.debug(
        `Address found for zipcode ${cleanZipcode}:`,
        addressData,
      );
      return addressData;
    } catch (error) {
      this.logger.error(
        `Error fetching address for zipcode ${zipcode}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Validates if a zipcode has the correct format
   */
  isValidZipcode(zipcode: string): boolean {
    const cleanZipcode = zipcode.replace(/\D/g, '');
    return cleanZipcode.length === 8;
  }

  /**
   * Formats zipcode with dash (12345-678)
   */
  formatZipcode(zipcode: string): string {
    const cleanZipcode = zipcode.replace(/\D/g, '');
    if (cleanZipcode.length === 8) {
      return `${cleanZipcode.slice(0, 5)}-${cleanZipcode.slice(5)}`;
    }
    return zipcode;
  }
}
