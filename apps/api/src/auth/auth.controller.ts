import {
  Controller,
  Post,
  Body,
  Get,
  Patch,
  UseGuards,
  UseInterceptors,
  ClassSerializerInterceptor,
  Param,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { UserEntity } from '../user/entities/user.entity';
import { ViaCepService } from '../shared/services/viacep.service';

@Controller('auth')
@UseInterceptors(ClassSerializerInterceptor)
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly viaCepService: ViaCepService,
  ) {}

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  async getProfile(
    @CurrentUser() user: { id: number; email: string; role: string },
  ): Promise<UserEntity> {
    return this.authService.getProfile(user.id);
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  async changePassword(
    @CurrentUser() user: { id: number; email: string; role: string },
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    return this.authService.changePassword(user.id, changePasswordDto);
  }

  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  async updateProfile(
    @CurrentUser() user: { id: number; email: string; role: string },
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<UserEntity> {
    return this.authService.updateProfile(user.id, updateProfileDto);
  }

  @Get('validate-cep/:cep')
  async validateCep(@Param('cep') cep: string) {
    try {
      // Validate CEP format
      if (!this.viaCepService.isValidZipcode(cep)) {
        throw new HttpException('Invalid CEP format', HttpStatus.BAD_REQUEST);
      }

      // Get address data from ViaCEP
      const addressData = await this.viaCepService.getAddressByZipcode(cep);

      if (!addressData) {
        throw new HttpException('CEP not found', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: addressData,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Error validating CEP',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
