import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const updateProfileSchema = z.object({
  name: z
    .string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .optional(),
  email: z
    .string()
    .email('Email deve ter um formato válido')
    .toLowerCase()
    .optional(),
  phone: z
    .string()
    .regex(/^\+?[\d\s()+-]{10,20}$/, 'Telefone deve ter um formato válido')
    .optional()
    .or(z.literal('')), // Permite string vazia
  photoUrl: z
    .string()
    .url('URL da foto deve ser válida')
    .optional()
    .or(z.literal('')), // Permite string vazia
  document: z
    .string()
    .max(20, 'Documento deve ter no máximo 20 caracteres')
    .optional()
    .or(z.literal('')), // Permite string vazia
  documentType: z.enum(['CPF', 'CNPJ']).optional(),
  // Address fields (optional)
  zipcode: z
    .string()
    .max(20, 'CEP deve ter no máximo 20 caracteres')
    .optional()
    .or(z.literal('')),
  street: z
    .string()
    .max(255, 'Rua deve ter no máximo 255 caracteres')
    .optional()
    .or(z.literal('')),
  neighborhood: z
    .string()
    .max(100, 'Bairro deve ter no máximo 100 caracteres')
    .optional()
    .or(z.literal('')),
  complement: z
    .string()
    .max(100, 'Complemento deve ter no máximo 100 caracteres')
    .optional()
    .or(z.literal('')),
  city: z
    .string()
    .max(100, 'Cidade deve ter no máximo 100 caracteres')
    .optional()
    .or(z.literal('')),
  state: z
    .string()
    .max(50, 'Estado deve ter no máximo 50 caracteres')
    .optional()
    .or(z.literal('')),
  country: z
    .string()
    .max(50, 'País deve ter no máximo 50 caracteres')
    .optional()
    .or(z.literal('')),
  streetNumber: z
    .string()
    .max(20, 'Número deve ter no máximo 20 caracteres')
    .optional()
    .or(z.literal('')),
});

export class UpdateProfileDto extends createZodDto(updateProfileSchema) {
  name?: string;
  email?: string;
  phone?: string;
  photoUrl?: string;
  document?: string;
  documentType?: 'CPF' | 'CNPJ';
  // Address fields (optional)
  zipcode?: string;
  street?: string;
  neighborhood?: string;
  complement?: string;
  city?: string;
  state?: string;
  country?: string;
  streetNumber?: string;
}
