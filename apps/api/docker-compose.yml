services:
  postgres:
    image: postgres:15-alpine
    container_name: carlao_postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: carlao_db
    ports:
      - '5433:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - carlao_network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: carlao_app
    restart: unless-stopped
    environment:
      DATABASE_URL: '********************************************/carlao_db?schema=public'
      JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production'
      JWT_EXPIRES_IN: '7d'
      PORT: 3000
      NODE_ENV: 'production'
    ports:
      - '3000:3000'
    depends_on:
      - postgres
    networks:
      - carlao_network

volumes:
  postgres_data:

networks:
  carlao_network:
    driver: bridge
